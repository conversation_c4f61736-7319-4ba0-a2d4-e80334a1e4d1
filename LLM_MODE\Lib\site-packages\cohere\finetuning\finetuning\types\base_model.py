# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from .base_type import BaseType
from .strategy import Strategy
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class BaseModel(UncheckedBaseModel):
    """
    The base model used for fine-tuning.
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the base model.
    """

    version: typing.Optional[str] = pydantic.Field(default=None)
    """
    read-only. The version of the base model.
    """

    base_type: BaseType = pydantic.Field()
    """
    The type of the base model.
    """

    strategy: typing.Optional[Strategy] = pydantic.Field(default=None)
    """
    Deprecated: The fine-tuning strategy.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
