# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class FinetuneDatasetMetrics(UncheckedBaseModel):
    trainable_token_count: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of tokens of valid examples that can be used for training.
    """

    total_examples: typing.Optional[int] = pydantic.Field(default=None)
    """
    The overall number of examples.
    """

    train_examples: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of training examples.
    """

    train_size_bytes: typing.Optional[int] = pydantic.Field(default=None)
    """
    The size in bytes of all training examples.
    """

    eval_examples: typing.Optional[int] = pydantic.Field(default=None)
    """
    Number of evaluation examples.
    """

    eval_size_bytes: typing.Optional[int] = pydantic.Field(default=None)
    """
    The size in bytes of all eval examples.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
