# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .connector import Connector
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ListConnectorsResponse(UncheckedBaseModel):
    connectors: typing.List[Connector]
    total_count: typing.Optional[float] = pydantic.Field(default=None)
    """
    Total number of connectors.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
