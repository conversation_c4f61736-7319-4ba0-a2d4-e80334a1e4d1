# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
import typing
from .finetuned_model import FinetunedModel
import pydantic
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class GetFinetunedModelResponse(UncheckedBaseModel):
    """
    Response to a request to get a fine-tuned model.
    """

    finetuned_model: typing.Optional[FinetunedModel] = pydantic.Field(default=None)
    """
    Information about the fine-tuned model.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
