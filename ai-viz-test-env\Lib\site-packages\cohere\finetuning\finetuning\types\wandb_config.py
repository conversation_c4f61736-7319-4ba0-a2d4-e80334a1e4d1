# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
import pydantic
import typing
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class WandbConfig(UncheckedBaseModel):
    """
    The Weights & Biases configuration.
    """

    project: str = pydantic.Field()
    """
    The WandB project name to be used during training.
    """

    api_key: str = pydantic.Field()
    """
    The WandB API key to be used during training.
    """

    entity: typing.Optional[str] = pydantic.Field(default=None)
    """
    The WandB entity name to be used during training.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
