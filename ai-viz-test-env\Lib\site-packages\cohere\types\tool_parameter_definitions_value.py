# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ToolParameterDefinitionsValue(UncheckedBaseModel):
    description: typing.Optional[str] = pydantic.Field(default=None)
    """
    The description of the parameter.
    """

    type: str = pydantic.Field()
    """
    The type of the parameter. Must be a valid Python type.
    """

    required: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Denotes whether the parameter is always present (required) or not. Defaults to not required.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
