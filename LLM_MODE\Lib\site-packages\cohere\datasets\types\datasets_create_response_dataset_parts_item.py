# This file was auto-generated by Fern from our API Definition.

from ...core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class DatasetsCreateResponseDatasetPartsItem(UncheckedBaseModel):
    """
    the underlying files that make up the dataset
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    the name of the dataset part
    """

    num_rows: typing.Optional[float] = pydantic.Field(default=None)
    """
    the number of rows in the dataset part
    """

    samples: typing.Optional[typing.List[str]] = None
    part_kind: typing.Optional[str] = pydantic.Field(default=None)
    """
    the kind of dataset part
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
