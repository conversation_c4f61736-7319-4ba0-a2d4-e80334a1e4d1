"""Public types to ease integrating with `altair`."""

from __future__ import annotations

__all__ = [
    "ChannelAngle",
    "ChannelColor",
    "ChannelColumn",
    "ChannelDescription",
    "ChannelDetail",
    "ChannelFacet",
    "ChannelFill",
    "ChannelFillOpacity",
    "ChannelHref",
    "ChannelKey",
    "ChannelLatitude",
    "ChannelLatitude2",
    "ChannelLongitude",
    "ChannelLongitude2",
    "ChannelOpacity",
    "ChannelOrder",
    "ChannelRadius",
    "ChannelRadius2",
    "ChannelRow",
    "ChannelShape",
    "ChannelSize",
    "ChannelStroke",
    "ChannelStrokeDash",
    "ChannelStrokeOpacity",
    "ChannelStrokeWidth",
    "ChannelText",
    "ChannelTheta",
    "ChannelTheta2",
    "ChannelTooltip",
    "ChannelUrl",
    "ChannelX",
    "ChannelX2",
    "ChannelXError",
    "ChannelXError2",
    "ChannelXOffset",
    "ChannelY",
    "ChannelY2",
    "ChannelYError",
    "ChannelYError2",
    "ChannelYOffset",
    "ChartType",
    "EncodeKwds",
    "Optional",
    "is_chart_type",
]

from altair.utils.schemapi import Optional
from altair.vegalite.v5.api import ChartType, is_chart_type
from altair.vegalite.v5.schema.channels import (
    ChannelAngle,
    ChannelColor,
    ChannelColumn,
    ChannelDescription,
    ChannelDetail,
    ChannelFacet,
    ChannelFill,
    ChannelFillOpacity,
    ChannelHref,
    ChannelKey,
    ChannelLatitude,
    ChannelLatitude2,
    ChannelLongitude,
    ChannelLongitude2,
    ChannelOpacity,
    ChannelOrder,
    ChannelRadius,
    ChannelRadius2,
    ChannelRow,
    ChannelShape,
    ChannelSize,
    ChannelStroke,
    ChannelStrokeDash,
    ChannelStrokeOpacity,
    ChannelStrokeWidth,
    ChannelText,
    ChannelTheta,
    ChannelTheta2,
    ChannelTooltip,
    ChannelUrl,
    ChannelX,
    ChannelX2,
    ChannelXError,
    ChannelXError2,
    ChannelXOffset,
    ChannelY,
    ChannelY2,
    ChannelYError,
    ChannelYError2,
    ChannelYOffset,
    EncodeKwds,
)
