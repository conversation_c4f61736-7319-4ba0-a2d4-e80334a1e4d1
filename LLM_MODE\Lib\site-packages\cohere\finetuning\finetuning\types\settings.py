# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
from .base_model import BaseModel
import pydantic
import typing
from .hyperparameters import Hyperparameters
from .wandb_config import WandbConfig
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class Settings(UncheckedBaseModel):
    """
    The configuration used for fine-tuning.
    """

    base_model: BaseModel = pydantic.Field()
    """
    The base model to fine-tune.
    """

    dataset_id: str = pydantic.Field()
    """
    The data used for training and evaluating the fine-tuned model.
    """

    hyperparameters: typing.Optional[Hyperparameters] = pydantic.Field(default=None)
    """
    Fine-tuning hyper-parameters.
    """

    multi_label: typing.Optional[bool] = pydantic.Field(default=None)
    """
    read-only. Whether the model is single-label or multi-label (only for classification).
    """

    wandb: typing.Optional[WandbConfig] = pydantic.Field(default=None)
    """
    The Weights & Biases configuration (Chat fine-tuning only).
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
