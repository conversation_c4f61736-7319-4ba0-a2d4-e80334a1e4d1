# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from .lora_target_modules import LoraTargetModules
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class Hyperparameters(UncheckedBaseModel):
    """
    The fine-tuning hyperparameters.
    """

    early_stopping_patience: typing.Optional[int] = pydantic.Field(default=None)
    """
    Stops training if the loss metric does not improve beyond the value of
    `early_stopping_threshold` after this many times of evaluation.
    """

    early_stopping_threshold: typing.Optional[float] = pydantic.Field(default=None)
    """
    How much the loss must improve to prevent early stopping.
    """

    train_batch_size: typing.Optional[int] = pydantic.Field(default=None)
    """
    The batch size is the number of training examples included in a single
    training pass.
    """

    train_epochs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of epochs to train for.
    """

    learning_rate: typing.Optional[float] = pydantic.Field(default=None)
    """
    The learning rate to be used during training.
    """

    lora_alpha: typing.Optional[int] = pydantic.Field(default=None)
    """
    Controls the scaling factor for LoRA updates. Higher values make the
    updates more impactful.
    """

    lora_rank: typing.Optional[int] = pydantic.Field(default=None)
    """
    Specifies the rank for low-rank matrices. Lower ranks reduce parameters
    but may limit model flexibility.
    """

    lora_target_modules: typing.Optional[LoraTargetModules] = pydantic.Field(default=None)
    """
    The combination of LoRA modules to target.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
