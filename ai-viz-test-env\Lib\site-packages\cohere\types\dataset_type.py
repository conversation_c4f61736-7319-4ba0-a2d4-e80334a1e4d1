# This file was auto-generated by Fern from our API Definition.

import typing

DatasetType = typing.Union[
    typing.Literal[
        "embed-input",
        "embed-result",
        "cluster-result",
        "cluster-outliers",
        "reranker-finetune-input",
        "single-label-classification-finetune-input",
        "chat-finetune-input",
        "multi-label-classification-finetune-input",
    ],
    typing.Any,
]
