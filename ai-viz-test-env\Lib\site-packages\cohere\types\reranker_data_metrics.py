# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class RerankerDataMetrics(UncheckedBaseModel):
    num_train_queries: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of training queries.
    """

    num_train_relevant_passages: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all relevant passages of valid training examples.
    """

    num_train_hard_negatives: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all hard negatives of valid training examples.
    """

    num_eval_queries: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of evaluation queries.
    """

    num_eval_relevant_passages: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all relevant passages of valid eval examples.
    """

    num_eval_hard_negatives: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all hard negatives of valid eval examples.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
